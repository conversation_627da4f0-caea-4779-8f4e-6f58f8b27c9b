%% 分析2信道数字信道化接收机（针对50MHz带宽优化）
clc; clear all; close all;

fprintf('=== 2信道数字信道化接收机分析（50MHz带宽优化）===\n');

%% 1. 理解2信道化算法
% 接收机参数
fs = 200e6;        % 采样率 200 MHz
D = 2;             % 2通道
load coef_lpf.mat;
h = coef_lpf;

% 分析多相滤波器
h_channel = zeros(D, length(h));
for j = 1:D
    h_channel(j,:) = downsample(h.*exp((-1j*2*pi*((j-1)*(0:length(h)-1)))/D), 1);
end

% 分析各信道的频率响应
N_fft = 2048;
f = linspace(0, fs, N_fft);
H_channel = zeros(D, N_fft);

for j = 1:D
    H_temp = fft(h_channel(j,:), N_fft);
    H_channel(j,:) = abs(H_temp);
end

% 找到每个信道的中心频率和带宽
channel_center_freq = zeros(1, D);
channel_bandwidth = zeros(1, D);

for j = 1:D
    [max_val, max_idx] = max(H_channel(j,:));
    channel_center_freq(j) = f(max_idx);
    
    % 计算3dB带宽
    half_max = max_val / sqrt(2);
    indices = find(H_channel(j,:) >= half_max);
    if ~isempty(indices)
        channel_bandwidth(j) = (f(indices(end)) - f(indices(1)));
    end
end

fprintf('2信道特性分析:\n');
fprintf('理论每信道带宽: %.1f MHz\n', fs/D/1e6);
for j = 1:D
    fprintf('  信道 %d: 中心频率 %.1f MHz, 3dB带宽 %.1f MHz\n', ...
        j, channel_center_freq(j)/1e6, channel_bandwidth(j)/1e6);
end

%% 2. 测试50MHz LFM信号
fprintf('\n=== 50MHz LFM信号测试 ===\n');

% 测试不同中频的50MHz LFM信号
test_center_freqs = [25, 50, 75, 100, 125, 150] * 1e6;  % MHz
B = 50e6;  % 50MHz带宽
T = 1e-6;  % 1us脉宽
N_pulse = round(T * fs);
t_pulse = (0:N_pulse-1) / fs;
k = B / T;

% 扩展到5us
T_total = 5e-6;
N_total = round(T_total * fs);

for fc = test_center_freqs
    % 生成50MHz LFM信号
    lfm_sig = exp(1j * (2*pi*fc*t_pulse + pi*k*t_pulse.^2));
    sig_extended = [lfm_sig, zeros(1, N_total - length(lfm_sig))];
    
    % 信道化
    y = zeros(D, length(sig_extended));
    for j = 1:D
        y(j,:) = filter(h_channel(j,:), 1, sig_extended);
    end
    
    % 分析结果
    power = zeros(1, D);
    for j = 1:D
        power(j) = sum(abs(y(j,:)).^2);
    end
    
    [max_power, max_channel] = max(power);
    display_channel = D - max_channel + 1;  % rec.m风格
    
    % 计算跨信道程度
    power_ratio = power(2) / power(1);  % 信道2与信道1的功率比
    if power_ratio > 0.1 && power_ratio < 10  % 如果两个信道都有显著功率
        cross_channel = true;
    else
        cross_channel = false;
    end
    
    fprintf('%.0f MHz LFM (50MHz带宽):\n', fc/1e6);
    fprintf('  最强信道: %d (rec.m显示: "信道%d")\n', max_channel, display_channel);
    fprintf('  功率分布: 信道1:%.1fdB 信道2:%.1fdB\n', ...
        10*log10(power(1)/max_power), 10*log10(power(2)/max_power));
    if cross_channel
        fprintf('  ⚠️  跨信道: 是\n');
    else
        fprintf('  ✅ 跨信道: 否\n');
    end
    fprintf('\n');
end

%% 3. 寻找最佳中频
fprintf('=== 寻找最佳中频（最小跨信道）===\n');

best_fc = 0;
min_cross_channel = inf;
best_channel = 0;

% 精细搜索最佳中频
search_freqs = 10e6:5e6:190e6;  % 10-190MHz，步进5MHz

for fc = search_freqs
    % 生成50MHz LFM信号
    lfm_sig = exp(1j * (2*pi*fc*t_pulse + pi*k*t_pulse.^2));
    sig_extended = [lfm_sig, zeros(1, N_total - length(lfm_sig))];
    
    % 信道化
    y = zeros(D, length(sig_extended));
    for j = 1:D
        y(j,:) = filter(h_channel(j,:), 1, sig_extended);
    end
    
    % 分析结果
    power = zeros(1, D);
    for j = 1:D
        power(j) = sum(abs(y(j,:)).^2);
    end
    
    % 计算跨信道程度（功率分散度）
    [max_power, max_channel] = max(power);
    other_power = sum(power) - max_power;
    cross_channel_ratio = other_power / max_power;
    
    if cross_channel_ratio < min_cross_channel
        min_cross_channel = cross_channel_ratio;
        best_fc = fc;
        best_channel = max_channel;
    end
end

fprintf('最佳中频选择:\n');
fprintf('  最佳中频: %.0f MHz\n', best_fc/1e6);
fprintf('  对应信道: %d\n', best_channel);
fprintf('  跨信道程度: %.3f (越小越好)\n', min_cross_channel);

%% 4. 可视化结果
figure('Name', '2信道接收机特性分析');

% 子图1: 各信道频率响应
subplot(2,2,1);
for j = 1:D
    plot(f/1e6, 20*log10(H_channel(j,:) + eps));
    hold on;
end
title('2信道频率响应');
xlabel('频率 (MHz)'); ylabel('幅度 (dB)');
legend('信道1', '信道2');
grid on;
xlim([0, 200]);

% 子图2: 信道中心频率和带宽
subplot(2,2,2);
bar(1:D, channel_center_freq/1e6);
title('各信道中心频率');
xlabel('信道号'); ylabel('中心频率 (MHz)');
grid on;
% 添加带宽信息
for j = 1:D
    text(j, channel_center_freq(j)/1e6 + 10, sprintf('BW:%.0fMHz', channel_bandwidth(j)/1e6), ...
        'HorizontalAlignment', 'center', 'FontSize', 10);
end

% 子图3: 50MHz信号在不同中频下的跨信道情况
subplot(2,2,3);
cross_channel_data = [];
freq_labels = [];
for fc = [25, 50, 75, 100, 125, 150] * 1e6
    % 重新计算这些频率的跨信道情况
    lfm_sig = exp(1j * (2*pi*fc*t_pulse + pi*k*t_pulse.^2));
    sig_extended = [lfm_sig, zeros(1, N_total - length(lfm_sig))];
    
    y = zeros(D, length(sig_extended));
    for j = 1:D
        y(j,:) = filter(h_channel(j,:), 1, sig_extended);
    end
    
    power = zeros(1, D);
    for j = 1:D
        power(j) = sum(abs(y(j,:)).^2);
    end
    
    cross_channel_data = [cross_channel_data; power/max(power)];
    freq_labels = [freq_labels; fc/1e6];
end

bar(freq_labels, cross_channel_data);
title('50MHz LFM信号跨信道分析');
xlabel('中频 (MHz)'); ylabel('归一化功率');
legend('信道1', '信道2');
grid on;

% 子图4: 最佳中频验证
subplot(2,2,4);
% 显示最佳中频的信号分布
fc = best_fc;
lfm_sig = exp(1j * (2*pi*fc*t_pulse + pi*k*t_pulse.^2));
sig_extended = [lfm_sig, zeros(1, N_total - length(lfm_sig))];

y = zeros(D, length(sig_extended));
for j = 1:D
    y(j,:) = filter(h_channel(j,:), 1, sig_extended);
end

x_fre = linspace(0, fs, length(sig_extended));
plot(x_fre/1e6, abs(fft(sig_extended)), 'k-', 'LineWidth', 2);
hold on;
for j = 1:D
    plot(x_fre/1e6, abs(fft(y(j,:))));
end
title(sprintf('最佳中频 %.0f MHz 的信号分布', best_fc/1e6));
xlabel('频率 (MHz)'); ylabel('幅度');
legend('输入信号', '信道1输出', '信道2输出');
grid on;

%% 5. 输出建议
fprintf('\n=== 2信道配置建议 ===\n');
fprintf('用于extract_pulse_from_simulation.m的参数:\n');
fprintf('D = 2;  %% 2信道配置\n');
fprintf('channel_center_freqs = [%.1f, %.1f];  %% MHz\n', ...
    channel_center_freq(1)/1e6, channel_center_freq(2)/1e6);
fprintf('推荐中频: %.0f MHz (最小跨信道)\n', best_fc/1e6);

fprintf('\n优势:\n');
fprintf('  - 每信道带宽: ~%.0f MHz\n', mean(channel_bandwidth)/1e6);
fprintf('  - 50MHz LFM信号跨信道风险: 最小\n');
fprintf('  - 处理简单，计算量小\n');

fprintf('\n=== 2信道分析完成 ===\n');
