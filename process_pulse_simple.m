%% 简化版脉冲串处理（自动适配变量）
clc; clear all; close all;

%% 1. 运行雷达仿真
fprintf('=== 运行雷达仿真 ===\n');
main;

if ~exist('IF_Signals', 'var')
    error('未找到IF_Signals变量');
end

[num_pulses, max_samples] = size(IF_Signals);
fprintf('获得脉冲串: %d个脉冲，每脉冲最大%d采样点\n', num_pulses, max_samples);

%% 2. 自动检测脉冲参数
fprintf('\n=== 自动检测脉冲参数 ===\n');

% 自动检测每个脉冲的有效长度
pulse_lengths = zeros(1, num_pulses);
for i = 1:num_pulses
    pulse_data = IF_Signals(i, :);
    % 找到最后一个非零元素
    nonzero_idx = find(pulse_data ~= 0, 1, 'last');
    if ~isempty(nonzero_idx)
        pulse_lengths(i) = nonzero_idx;
    else
        pulse_lengths(i) = max_samples;
    end
end

fprintf('脉冲长度统计:\n');
fprintf('  最短脉冲: %d 采样点\n', min(pulse_lengths));
fprintf('  最长脉冲: %d 采样点\n', max(pulse_lengths));
fprintf('  平均长度: %.0f 采样点\n', mean(pulse_lengths));

% 获取载频信息
if exist('fc_10ms', 'var') && length(fc_10ms) >= num_pulses
    carrier_freqs = fc_10ms(1:num_pulses);
    fprintf('载频信息: 从fc_10ms获取\n');
elseif exist('IF_BP', 'var') && isfield(IF_BP, 'fc_list')
    % 循环使用载频列表
    fc_list = IF_BP.fc_list;
    carrier_freqs = fc_list(mod(0:num_pulses-1, length(fc_list)) + 1);
    fprintf('载频信息: 从IF_BP.fc_list循环获取\n');
else
    % 使用固定载频
    carrier_freqs = 8e9 * ones(1, num_pulses);
    fprintf('载频信息: 使用默认8GHz\n');
end

fprintf('载频范围: %.3f - %.3f GHz\n', min(carrier_freqs)/1e9, max(carrier_freqs)/1e9);

%% 3. 接收机配置
fprintf('\n=== 接收机配置 ===\n');
load coef_lpf.mat;
h = coef_lpf;
D = 2;  % 2信道
IF_fs = 200e6;
target_IF_fc = 75e6;  % 75MHz中频

% 信道化滤波器
h_channel = zeros(D, length(h));
for j = 1:D
    h_channel(j,:) = downsample(h.*exp((-1j*2*pi*((j-1)*(0:length(h)-1)))/D), 1);
end

channel_center_freqs = [10.9, 89.1];  % MHz
fprintf('2信道配置，中心频率: [%.1f, %.1f] MHz\n', channel_center_freqs);

%% 4. 批量处理脉冲
fprintf('\n=== 批量处理脉冲 ===\n');
max_process = min(50, num_pulses);  % 最多处理50个脉冲
fprintf('处理脉冲数量: %d\n', max_process);

% 存储结果
results = struct();
results.pulse_idx = [];
results.rf_freq = [];
results.pulse_length = [];
results.max_channel = [];
results.channel_power = [];
results.cross_channel_ratio = [];

% 获取采样率
if exist('IF_BP', 'var') && isfield(IF_BP, 'fs')
    RF_fs = IF_BP.fs;
else
    RF_fs = 2.4e9;  % 默认采样率
end

for i = 1:max_process
    % 提取脉冲
    pulse_len = pulse_lengths(i);
    RF_pulse = IF_Signals(i, 1:pulse_len);
    RF_fc = carrier_freqs(i);
    
    % 转换为复数
    RF_pulse_complex = hilbert(RF_pulse);
    
    % 下变频
    try
        [IF_pulse, ~, ~] = downconvert_rf_to_if(RF_pulse_complex, RF_fc, RF_fs, target_IF_fc, IF_fs);
    catch
        % 如果下变频失败，跳过这个脉冲
        fprintf('脉冲 %d 下变频失败，跳过\n', i);
        continue;
    end
    
    % 调整长度到标准长度
    T_rec = 5e-6;
    N_rec = round(T_rec * IF_fs);
    if length(IF_pulse) < N_rec
        sig_for_rec = [IF_pulse, zeros(1, N_rec - length(IF_pulse))];
    else
        sig_for_rec = IF_pulse(1:N_rec);
    end
    
    % 信道化
    y = zeros(D, length(sig_for_rec));
    for j = 1:D
        y(j,:) = filter(h_channel(j,:), 1, sig_for_rec);
    end
    
    % 计算功率
    channel_power = zeros(1, D);
    for j = 1:D
        channel_power(j) = sum(abs(y(j,:)).^2);
    end
    
    [max_power, max_channel] = max(channel_power);
    
    % 计算跨信道程度
    other_power = sum(channel_power) - max_power;
    cross_ratio = other_power / max_power;
    
    % 存储结果
    results.pulse_idx = [results.pulse_idx, i];
    results.rf_freq = [results.rf_freq, RF_fc];
    results.pulse_length = [results.pulse_length, pulse_len];
    results.max_channel = [results.max_channel, max_channel];
    results.channel_power = [results.channel_power; channel_power];
    results.cross_channel_ratio = [results.cross_channel_ratio, cross_ratio];
    
    if mod(i, 10) == 0
        fprintf('已处理 %d/%d 个脉冲\n', i, max_process);
    end
end

processed_count = length(results.pulse_idx);
fprintf('成功处理 %d 个脉冲\n', processed_count);

%% 5. 统计分析
fprintf('\n=== 统计分析 ===\n');

if processed_count == 0
    fprintf('没有成功处理的脉冲，无法进行统计分析\n');
    return;
end

% 信道使用统计
channel_usage = zeros(1, D);
for i = 1:processed_count
    channel_usage(results.max_channel(i)) = channel_usage(results.max_channel(i)) + 1;
end

fprintf('信道使用统计:\n');
for i = 1:D
    usage_percent = channel_usage(i) / processed_count * 100;
    fprintf('  信道 %d: %d次 (%.1f%%)\n', i, channel_usage(i), usage_percent);
end

% 载频分析
unique_freqs = unique(results.rf_freq);
fprintf('\n载频分析:\n');
fprintf('  不同载频数量: %d\n', length(unique_freqs));
fprintf('  载频范围: %.3f - %.3f GHz\n', min(unique_freqs)/1e9, max(unique_freqs)/1e9);

% 跨信道分析
avg_cross_ratio = mean(results.cross_channel_ratio);
fprintf('\n跨信道分析:\n');
fprintf('  平均跨信道比例: %.3f\n', avg_cross_ratio);
fprintf('  跨信道比例范围: %.3f - %.3f\n', min(results.cross_channel_ratio), max(results.cross_channel_ratio));

%% 6. 可视化
fprintf('\n=== 生成图形 ===\n');

figure('Name', '脉冲串处理结果');

% 子图1: 载频序列
subplot(2,3,1);
plot(results.pulse_idx, results.rf_freq/1e9, 'o-');
title('脉冲载频序列');
xlabel('脉冲编号'); ylabel('载频 (GHz)');
grid on;

% 子图2: 信道分布
subplot(2,3,2);
plot(results.pulse_idx, results.max_channel, 'o-');
title('最强信道分布');
xlabel('脉冲编号'); ylabel('信道号');
ylim([0.5, D+0.5]);
grid on;

% 子图3: 信道使用统计
subplot(2,3,3);
bar(1:D, channel_usage);
title('信道使用统计');
xlabel('信道号'); ylabel('使用次数');
grid on;

% 子图4: 载频vs信道
subplot(2,3,4);
scatter(results.rf_freq/1e9, results.max_channel, 50, 'filled');
title('载频 vs 最强信道');
xlabel('载频 (GHz)'); ylabel('信道号');
ylim([0.5, D+0.5]);
grid on;

% 子图5: 跨信道程度
subplot(2,3,5);
plot(results.pulse_idx, results.cross_channel_ratio, 'o-');
title('跨信道程度');
xlabel('脉冲编号'); ylabel('跨信道比例');
grid on;

% 子图6: 功率分布热图
subplot(2,3,6);
if processed_count > 1
    power_matrix = results.channel_power;
    % 归一化每行
    for i = 1:size(power_matrix, 1)
        power_matrix(i, :) = power_matrix(i, :) / max(power_matrix(i, :));
    end
    imagesc(1:D, 1:processed_count, 10*log10(power_matrix + eps));
    colorbar;
    title('各脉冲信道功率 (dB)');
    xlabel('信道号'); ylabel('脉冲编号');
else
    bar(1:D, 10*log10(results.channel_power/max(results.channel_power)));
    title('信道功率分布');
    xlabel('信道号'); ylabel('相对功率 (dB)');
end

%% 7. 性能总结
fprintf('\n=== 性能总结 ===\n');

% 信道平衡度
if std(channel_usage) > 0
    balance = std(channel_usage) / mean(channel_usage);
    fprintf('信道平衡度: %.3f (越小越平衡)\n', balance);
else
    fprintf('信道平衡度: 完全平衡\n');
end

% 处理成功率
success_rate = processed_count / max_process * 100;
fprintf('处理成功率: %.1f%%\n', success_rate);

% 推荐的最佳载频
for ch = 1:D
    ch_indices = find(results.max_channel == ch);
    if ~isempty(ch_indices)
        ch_freqs = results.rf_freq(ch_indices);
        avg_freq = mean(ch_freqs);
        fprintf('信道 %d 的平均载频: %.3f GHz\n', ch, avg_freq/1e9);
    end
end

fprintf('\n=== 脉冲串处理完成 ===\n');
fprintf('🎉 成功分析了 %d 个脉冲的信道化特性！\n', processed_count);
