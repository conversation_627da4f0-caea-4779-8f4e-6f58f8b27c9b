function [PRI_list,total_pluse_num]=pri(PRI,PulseNum,PRI_variance,cycle_index,dither,delta)
PRI_list=[];
PRI_Rand_Index=[];
if PRI_variance==1
    parfor i=1:cycle_index
        for j=1:length(PRI)
            PRI_list=[PRI_list,PRI(j)];
        end
    end
    total_pluse_num=cycle_index*length(PRI);
elseif PRI_variance==2
    parfor c=1:cycle_index
        for i=1:length(PRI)
            for j=1:PulseNum(i)
                 PRI_list=[PRI_list,PRI(i)];
            end
        end
    end
    total_pluse_num=sum(cycle_index*PulseNum);
elseif PRI_variance==3
    parfor i=1:cycle_index
         PRI_list=[PRI_list,PRI*(1+dither*(2*rand(1)-1))];
    end
    total_pluse_num=cycle_index;
elseif PRI_variance==4
    parfor i=1:cycle_index
        for j=1:PulseNum
            PRI_list=[PRI_list,PRI+j*delta];
        end
    end
    total_pluse_num=sum(cycle_index*PulseNum);
elseif PRI_variance==5
    PRI_len=length(PRI);
    for i=1:cycle_index
        pri_rand_index=randi(PRI_len);
        PRI_Rand_Index=[PRI_Rand_Index,pri_rand_index];
        PRI_list=[PRI_list,PRI(pri_rand_index)];
    end
    total_pluse_num=cycle_index;
elseif PRI_variance==6
    PRI_len=length(PRI);
    for i=1:cycle_index
        pri_rand_index=randi(PRI_len);
        for j=1:PulseNum
            PRI_Rand_Index=[PRI_Rand_Index,pri_rand_index];
            PRI_list=[PRI_list,PRI(pri_rand_index)];
        end
    end
    total_pluse_num=sum(cycle_index*PulseNum);
else
    for i=1:cycle_index
        PRI_list=[PRI_list,PRI];
    end
    total_pluse_num=cycle_index;
end
