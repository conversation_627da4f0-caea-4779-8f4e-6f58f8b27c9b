%% 从现有仿真结果中提取脉冲并送入接收机
clc; clear all; close all;

%% 1. 运行雷达仿真获取脉冲串
fprintf('=== 运行雷达仿真 ===\n');
main;  % 运行主仿真程序

% 检查是否成功生成了IF_Signals
if ~exist('IF_Signals', 'var')
    error('未找到IF_Signals变量，请确保main.m正常运行');
end

fprintf('仿真完成，获得脉冲串:\n');
fprintf('  脉冲数量: %d\n', size(IF_Signals, 1));
fprintf('  每脉冲采样点: %d\n', size(IF_Signals, 2));

%% 2. 提取第一个脉冲
fprintf('\n=== 提取第一个脉冲 ===\n');
pulse_index = 1;  % 选择第一个脉冲
RF_pulse_raw = IF_Signals(pulse_index, :);

% 去除零填充部分（只保留有效信号）
valid_length = Signal_lenlist(pulse_index);
RF_pulse = RF_pulse_raw(1:valid_length);

% 获取对应的载频
RF_fc = fc_10ms(pulse_index);

fprintf('提取的脉冲信息:\n');
fprintf('  脉冲索引: %d\n', pulse_index);
fprintf('  载频: %.3f GHz\n', RF_fc/1e9);
fprintf('  有效长度: %d 采样点\n', valid_length);
fprintf('  脉宽: %.2f us\n', valid_length/IF_BP.fs*1e6);

%% 3. 转换为复数信号（如果需要）
% 原始信号是实数，需要转换为复数进行下变频
% 使用希尔伯特变换生成解析信号
RF_pulse_complex = hilbert(RF_pulse);

%% 4. 下变频到中频
fprintf('\n=== 下变频处理 ===\n');

% 根据射频载频选择合适的中频
% 目标是将信号下变频到接收机能够处理的频率范围
if RF_fc > 5e9  % 如果是高频段（>5GHz）
    target_IF_fc = 50e6;   % 使用50MHz中频
elseif RF_fc > 2e9  % 中频段（2-5GHz）
    target_IF_fc = 30e6;   % 使用30MHz中频
else  % 低频段（<2GHz）
    target_IF_fc = 10e6;   % 使用10MHz中频
end

IF_fs = 200e6;        % 中频采样率 200 MHz

% 显示原始信号信息
fprintf('原始射频信号信息:\n');
fprintf('  载频: %.3f GHz\n', RF_fc/1e9);
fprintf('  采样率: %.1f GHz\n', IF_BP.fs/1e9);
fprintf('  信号长度: %d 采样点\n', length(RF_pulse_complex));
fprintf('  选择的目标中频: %.1f MHz\n', target_IF_fc/1e6);

[IF_pulse, IF_fc, t_if] = downconvert_rf_to_if(RF_pulse_complex, RF_fc, IF_BP.fs, target_IF_fc, IF_fs);

%% 5. 送入数字信道化接收机
fprintf('\n=== 数字信道化接收机处理 ===\n');

% 加载接收机参数
load coef_lpf.mat;
h = coef_lpf;
D = 8;  % 8通道

% 调整信号长度以匹配接收机
T_rec = 5e-6;  % 接收机时长
N_rec = round(T_rec * IF_fs);  % 确保是整数
if length(IF_pulse) < N_rec
    sig_for_rec = [IF_pulse, zeros(1, N_rec - length(IF_pulse))];
else
    sig_for_rec = IF_pulse(1:N_rec);
end

% 信道化处理
len = length(sig_for_rec);
y = zeros(D, len);
h_channel = zeros(D, length(h));
im = sqrt(-1);

for j = 1:D
    h_channel(j,:) = downsample(h.*exp((-im*2*pi*((j-1)*(0:length(h)-1)))/D), 1);
    y(j,:) = filter(h_channel(j,:), 1, sig_for_rec);
end

%% 6. 结果分析
fprintf('\n=== 结果分析 ===\n');

% 计算各信道功率
channel_power = zeros(1, D);
for i = 1:D
    channel_power(i) = sum(abs(y(i,:)).^2);
end

% 找到最强信道
[max_power, max_channel] = max(channel_power);

% 根据实际测试确定的信道-频率映射关系
channel_center_freqs = [10.9, 164.1, 139.1, 114.1, 89.1, 86.0, 39.1, 14.1];  % MHz

% 找到最接近目标频率的信道
freq_diff = abs(channel_center_freqs - target_IF_fc/1e6);
[~, expected_channel] = min(freq_diff);

fprintf('多相滤波器信道化分析:\n');
fprintf('  目标中频: %.1f MHz\n', target_IF_fc/1e6);
fprintf('  最接近的信道: %d (中心频率: %.1f MHz)\n', expected_channel, channel_center_freqs(expected_channel));
fprintf('  实际最强信道: %d (中心频率: %.1f MHz)\n', max_channel, channel_center_freqs(max_channel));

% 显示各信道功率（按实际处理顺序）
fprintf('\n各信道功率分布（实际处理顺序）:\n');
for i = 1:D
    power_db = 10*log10(channel_power(i)/max_power);
    fprintf('  信道 %d (中心: %.1f MHz): %.1f dB', i, channel_center_freqs(i), power_db);
    if i == max_channel
        fprintf(' ← 最强');
    end
    fprintf('\n');
end

% 显示rec.m风格的结果
fprintf('\nrec.m显示风格（倒序编号）:\n');
for iter = 1:D
    display_channel = D - iter + 1;
    power_db = 10*log10(channel_power(iter)/max_power);
    fprintf('  "信道%d": %.1f dB', display_channel, power_db);
    if iter == max_channel
        fprintf(' ← 最强');
    end
    fprintf('\n');
end

% 频率轴
x_fre1 = linspace(0, IF_fs, len);

% 显示原始射频脉冲
figure('Name', '射频脉冲时域波形');
subplot(2,1,1);
t_rf = (0:length(RF_pulse)-1) / IF_BP.fs;
plot(t_rf*1e6, real(RF_pulse_complex));
title('射频脉冲时域波形 (实部)');
xlabel('时间 (μs)'); ylabel('幅度');
grid on;

subplot(2,1,2);
plot(t_rf*1e6, abs(RF_pulse_complex));
title('射频脉冲包络');
xlabel('时间 (μs)'); ylabel('幅度');
grid on;

% 显示中频信号
figure('Name', '中频信号');
subplot(2,1,1);
plot(t_if*1e6, real(IF_pulse));
title('中频信号时域波形');
xlabel('时间 (μs)'); ylabel('幅度');
grid on;

subplot(2,1,2);
plot(x_fre1/1e6, abs(fft(sig_for_rec)));
title('中频信号频谱');
xlabel('频率 (MHz)'); ylabel('幅度');
grid on;

% 显示信道化结果（实际处理顺序）
figure('Name', '信道化输出（实际处理顺序）');
for iter = 1:D
    subplot(4, 2, iter);
    plot(x_fre1/1e6, abs(fft(y(iter,:))));

    % 构建标题
    title_str = sprintf('信道 %d (中心: %.1f MHz)', iter, channel_center_freqs(iter));
    if iter == max_channel
        title_str = [title_str, ' - 最强'];
        title(title_str, 'Color', 'red', 'FontWeight', 'bold');
    elseif iter == expected_channel
        title_str = [title_str, ' - 预期'];
        title(title_str, 'Color', 'blue');
    else
        title(title_str);
    end

    xlabel('频率 (MHz)'); ylabel('幅度');
    grid on;
end

% 显示rec.m风格的信道化结果
figure('Name', 'rec.m风格显示（倒序编号）');
for iter = 1:D
    subplot(4, 2, iter);
    plot(x_fre1/1e6, abs(fft(y(iter,:))));

    display_channel = D - iter + 1;
    title_str = sprintf('信道%d (实际信道%d)', display_channel, iter);
    if iter == max_channel
        title_str = [title_str, ' - 最强'];
        title(title_str, 'Color', 'red', 'FontWeight', 'bold');
    else
        title(title_str);
    end

    xlabel('频率 (MHz)'); ylabel('幅度');
    grid on;
end

% 功率分布图
figure('Name', '信道功率分布分析');
subplot(2,1,1);
bar(1:D, 10*log10(channel_power/max_power));
title('各信道功率分布（实际处理顺序）');
xlabel('信道号'); ylabel('相对功率 (dB)');
grid on;
% 标记最强信道和预期信道
hold on;
bar(max_channel, 10*log10(channel_power(max_channel)/max_power), 'r');
bar(expected_channel, 10*log10(channel_power(expected_channel)/max_power), 'g');
legend('信道功率', '最强信道', '预期信道');
% 添加中心频率标签
for i = 1:D
    text(i, 10*log10(channel_power(i)/max_power) + 2, sprintf('%.1f', channel_center_freqs(i)), ...
        'HorizontalAlignment', 'center', 'FontSize', 8);
end

subplot(2,1,2);
% rec.m风格的功率分布
rec_style_power = zeros(1, D);
for iter = 1:D
    rec_style_power(D - iter + 1) = channel_power(iter);
end
bar(1:D, 10*log10(rec_style_power/max_power));
title('各信道功率分布（rec.m显示风格）');
xlabel('信道号（rec.m编号）'); ylabel('相对功率 (dB)');
grid on;
% 标记最强信道
hold on;
display_max_channel = D - max_channel + 1;
bar(display_max_channel, 10*log10(rec_style_power(display_max_channel)/max_power), 'r');
legend('信道功率', '最强信道');

%% 7. 性能评估和总结
fprintf('\n=== 性能评估 ===\n');

% 计算信噪比（基于最强信道）
signal_power = channel_power(max_channel);
other_channels = channel_power;
other_channels(max_channel) = [];
noise_power = mean(other_channels);

if noise_power > 0
    snr_db = 10*log10(signal_power/noise_power);
    fprintf('信噪比: %.1f dB\n', snr_db);
else
    fprintf('信噪比: 很高 (噪声功率接近零)\n');
end

% 分析结果
freq_error = abs(channel_center_freqs(max_channel) - target_IF_fc/1e6);
fprintf('频率匹配分析:\n');
fprintf('  目标中频: %.1f MHz\n', target_IF_fc/1e6);
fprintf('  最强信道中心频率: %.1f MHz\n', channel_center_freqs(max_channel));
fprintf('  频率误差: %.1f MHz\n', freq_error);

if freq_error < 20  % 允许20MHz误差（考虑到LFM带宽）
    fprintf('✅ 成功: 雷达脉冲被正确信道化\n');
    fprintf('✅ 下变频和接收机工作正常\n');
else
    fprintf('⚠️  注意: 频率误差较大，可能需要调整中频选择\n');
end

% rec.m显示结果
display_max_channel = D - max_channel + 1;
fprintf('\nrec.m中的显示结果:\n');
fprintf('  雷达脉冲将在"信道%d"中显示最强响应\n', display_max_channel);

% 脉冲特性总结
fprintf('\n=== 脉冲特性总结 ===\n');
fprintf('原始雷达脉冲:\n');
fprintf('  射频载频: %.3f GHz\n', RF_fc/1e9);
fprintf('  脉宽: %.2f μs\n', valid_length/IF_BP.fs*1e6);
fprintf('  带宽: %.1f MHz\n', IF_BP.B/1e6);
fprintf('转换后中频脉冲:\n');
fprintf('  中频载频: %.1f MHz\n', target_IF_fc/1e6);
fprintf('  信道化结果: 信道%d最强\n', max_channel);
fprintf('  rec.m显示: "信道%d"\n', display_max_channel);

fprintf('\n=== 雷达脉冲接收机测试完成 ===\n');
fprintf('🎉 雷达仿真脉冲成功通过数字信道化接收机处理！\n');
