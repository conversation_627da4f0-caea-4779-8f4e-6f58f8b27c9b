%% 从现有仿真结果中提取脉冲并送入接收机
clc; clear all; close all;

%% 1. 运行雷达仿真获取脉冲串
fprintf('=== 运行雷达仿真 ===\n');
main;  % 运行主仿真程序

% 检查是否成功生成了IF_Signals
if ~exist('IF_Signals', 'var')
    error('未找到IF_Signals变量，请确保main.m正常运行');
end

fprintf('仿真完成，获得脉冲串:\n');
fprintf('  脉冲数量: %d\n', size(IF_Signals, 1));
fprintf('  每脉冲采样点: %d\n', size(IF_Signals, 2));

%% 2. 提取第一个脉冲
fprintf('\n=== 提取第一个脉冲 ===\n');
pulse_index = 1;  % 选择第一个脉冲
RF_pulse_raw = IF_Signals(pulse_index, :);

% 去除零填充部分（只保留有效信号）
valid_length = Signal_lenlist(pulse_index);
RF_pulse = RF_pulse_raw(1:valid_length);

% 获取对应的载频
RF_fc = fc_10ms(pulse_index);

fprintf('提取的脉冲信息:\n');
fprintf('  脉冲索引: %d\n', pulse_index);
fprintf('  载频: %.3f GHz\n', RF_fc/1e9);
fprintf('  有效长度: %d 采样点\n', valid_length);
fprintf('  脉宽: %.2f us\n', valid_length/IF_BP.fs*1e6);

%% 3. 转换为复数信号（如果需要）
% 原始信号是实数，需要转换为复数进行下变频
% 使用希尔伯特变换生成解析信号
RF_pulse_complex = hilbert(RF_pulse);

%% 4. 下变频到中频
fprintf('\n=== 下变频处理 ===\n');
target_IF_fc = 30e6;  % 目标中频 30 MHz
IF_fs = 200e6;        % 中频采样率 200 MHz

[IF_pulse, IF_fc, t_if] = downconvert_rf_to_if(RF_pulse_complex, RF_fc, IF_BP.fs, target_IF_fc, IF_fs);

%% 5. 送入数字信道化接收机
fprintf('\n=== 数字信道化接收机处理 ===\n');

% 加载接收机参数
load coef_lpf.mat;
h = coef_lpf;
D = 8;  % 8通道

% 调整信号长度以匹配接收机
T_rec = 5e-6;  % 接收机时长
N_rec = round(T_rec * IF_fs);  % 确保是整数
if length(IF_pulse) < N_rec
    sig_for_rec = [IF_pulse, zeros(1, N_rec - length(IF_pulse))];
else
    sig_for_rec = IF_pulse(1:N_rec);
end

% 信道化处理
len = length(sig_for_rec);
y = zeros(D, len);
h_channel = zeros(D, length(h));
im = sqrt(-1);

for j = 1:D
    h_channel(j,:) = downsample(h.*exp((-im*2*pi*((j-1)*(0:length(h)-1)))/D), 1);
    y(j,:) = filter(h_channel(j,:), 1, sig_for_rec);
end

%% 6. 结果显示
fprintf('\n=== 结果分析 ===\n');

% 频率轴
x_fre1 = linspace(0, IF_fs, len);

% 显示原始射频脉冲
figure('Name', '射频脉冲时域波形');
subplot(2,1,1);
t_rf = (0:length(RF_pulse)-1) / IF_BP.fs;
plot(t_rf*1e6, real(RF_pulse_complex));
title('射频脉冲时域波形 (实部)');
xlabel('时间 (μs)'); ylabel('幅度');
grid on;

subplot(2,1,2);
plot(t_rf*1e6, abs(RF_pulse_complex));
title('射频脉冲包络');
xlabel('时间 (μs)'); ylabel('幅度');
grid on;

% 显示中频信号
figure('Name', '中频信号');
subplot(2,1,1);
plot(t_if*1e6, real(IF_pulse));
title('中频信号时域波形');
xlabel('时间 (μs)'); ylabel('幅度');
grid on;

subplot(2,1,2);
plot(x_fre1/1e6, abs(fft(sig_for_rec)));
title('中频信号频谱');
xlabel('频率 (MHz)'); ylabel('幅度');
grid on;

% 显示信道化结果
figure('Name', '信道化输出');
for iter = 1:D
    subplot(4, 2, iter);
    plot(x_fre1/1e6, abs(fft(y(iter,:))));
    title(['信道 ', num2str(D - iter + 1)]);
    xlabel('频率 (MHz)'); ylabel('幅度');
    grid on;
end

% 分析结果
channel_power = zeros(1, D);
for i = 1:D
    channel_power(i) = sum(abs(y(i,:)).^2);
end

[max_power, max_channel] = max(channel_power);
expected_channel = round(IF_fc / (IF_fs/D)) + 1;

fprintf('信号分析结果:\n');
fprintf('  目标中频: %.1f MHz\n', IF_fc/1e6);
fprintf('  预期信道: %d\n', expected_channel);
fprintf('  实际最强信道: %d\n', max_channel);
fprintf('  匹配度: %s\n', iif(abs(expected_channel-max_channel)<=1, '良好', '需要调整'));

fprintf('\n=== 处理完成 ===\n');

function result = iif(condition, true_val, false_val)
    if condition
        result = true_val;
    else
        result = false_val;
    end
end
