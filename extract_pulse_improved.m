%% 改进的脉冲提取和接收机测试脚本
clc; clear all; close all;

%% 1. 直接生成测试用的中频LFM信号
fprintf('=== 生成测试用中频LFM信号 ===\n');

% 接收机参数
IF_fs = 200e6;        % 中频采样率 200 MHz
target_IF_fc = 30e6;  % 目标中频 30 MHz
T_pulse = 1e-6;       % 脉宽 1us
B = 30e6;             % 带宽 30 MHz

% 生成中频LFM信号
N_pulse = round(T_pulse * IF_fs);
t = (0:N_pulse-1) / IF_fs;
k = B / T_pulse;  % 调频斜率

% 生成复数LFM信号
IF_signal_test = exp(1j * (2*pi*target_IF_fc*t + pi*k*t.^2));

fprintf('测试信号参数:\n');
fprintf('  中频载频: %.1f MHz\n', target_IF_fc/1e6);
fprintf('  脉宽: %.1f us\n', T_pulse*1e6);
fprintf('  带宽: %.1f MHz\n', B/1e6);
fprintf('  采样率: %.1f MHz\n', IF_fs/1e6);
fprintf('  采样点数: %d\n', length(IF_signal_test));

%% 2. 适配接收机格式
fprintf('\n=== 适配接收机格式 ===\n');
T_rec = 5e-6;  % 接收机期望时长
N_rec = round(T_rec * IF_fs);

if length(IF_signal_test) < N_rec
    sig_for_rec = [IF_signal_test, zeros(1, N_rec - length(IF_signal_test))];
    fprintf('信号用零填充到 %d 点\n', N_rec);
else
    sig_for_rec = IF_signal_test(1:N_rec);
    fprintf('信号截取到 %d 点\n', N_rec);
end

%% 3. 数字信道化接收机处理
fprintf('\n=== 数字信道化接收机处理 ===\n');

% 加载滤波器系数
load coef_lpf.mat;
h = coef_lpf;
D = 8;  % 8通道

% 信道化处理
len = length(sig_for_rec);
y = zeros(D, len);
h_channel = zeros(D, length(h));

for j = 1:D
    h_channel(j,:) = downsample(h.*exp((-1j*2*pi*((j-1)*(0:length(h)-1)))/D), 1);
    y(j,:) = filter(h_channel(j,:), 1, sig_for_rec);
end

%% 4. 分析结果
fprintf('\n=== 结果分析 ===\n');

% 计算各信道功率
channel_power = zeros(1, D);
for i = 1:D
    channel_power(i) = sum(abs(y(i,:)).^2);
end

% 找到最强信道
[max_power, max_channel] = max(channel_power);

% 计算预期信道
% 信道化接收机的信道分配：信道i对应频率范围 [(i-1)*fs/D, i*fs/D]
channel_bw = IF_fs / D;  % 每个信道带宽 25 MHz
expected_channel = floor(target_IF_fc / channel_bw) + 1;

fprintf('信道分析:\n');
fprintf('  每信道带宽: %.1f MHz\n', channel_bw/1e6);
fprintf('  目标频率: %.1f MHz\n', target_IF_fc/1e6);
fprintf('  预期信道: %d (频率范围: %.1f-%.1f MHz)\n', expected_channel, ...
    (expected_channel-1)*channel_bw/1e6, expected_channel*channel_bw/1e6);
fprintf('  实际最强信道: %d\n', max_channel);

% 显示各信道功率
fprintf('\n各信道功率分布:\n');
for i = 1:D
    power_db = 10*log10(channel_power(i)/max_power);
    freq_range = sprintf('%.1f-%.1f MHz', (i-1)*channel_bw/1e6, i*channel_bw/1e6);
    fprintf('  信道 %d (%s): %.1f dB\n', i, freq_range, power_db);
end

%% 5. 可视化结果
fprintf('\n=== 生成图形 ===\n');

% 频率轴
x_fre = linspace(0, IF_fs, len);

% 图1: 输入信号
figure('Name', '输入信号分析');
subplot(2,2,1);
plot(t*1e6, real(IF_signal_test));
title('中频LFM信号时域波形');
xlabel('时间 (μs)'); ylabel('幅度');
grid on;

subplot(2,2,2);
plot(t*1e6, abs(IF_signal_test));
title('信号包络');
xlabel('时间 (μs)'); ylabel('幅度');
grid on;

subplot(2,2,3);
plot(x_fre/1e6, abs(fft(sig_for_rec)));
title('输入信号频谱');
xlabel('频率 (MHz)'); ylabel('幅度');
grid on;
% 标记目标频率
hold on;
plot([target_IF_fc/1e6, target_IF_fc/1e6], ylim, 'r--', 'LineWidth', 2);
legend('信号频谱', '目标频率');

subplot(2,2,4);
bar(1:D, 10*log10(channel_power/max_power));
title('各信道功率分布');
xlabel('信道号'); ylabel('相对功率 (dB)');
grid on;
% 标记预期信道
hold on;
bar(expected_channel, 10*log10(channel_power(expected_channel)/max_power), 'r');
legend('信道功率', '预期信道');

% 图2: 信道化输出详细分析
figure('Name', '信道化输出详细分析');
for iter = 1:D
    subplot(4, 2, iter);
    plot(x_fre/1e6, abs(fft(y(iter,:))));
    title(['信道 ', num2str(iter), ' (', sprintf('%.1f-%.1f MHz', ...
        (iter-1)*channel_bw/1e6, iter*channel_bw/1e6), ')']);
    xlabel('频率 (MHz)'); ylabel('幅度');
    grid on;
    
    % 如果是预期信道，用红色标题
    if iter == expected_channel
        title(['信道 ', num2str(iter), ' (预期信道)'], 'Color', 'red');
    end
end

%% 6. 性能评估
fprintf('\n=== 性能评估 ===\n');

% 计算信噪比
signal_power = channel_power(expected_channel);
noise_power = mean(channel_power) - signal_power/D;
if noise_power > 0
    snr_db = 10*log10(signal_power/noise_power);
    fprintf('信噪比: %.1f dB\n', snr_db);
else
    fprintf('信噪比: 很高 (噪声功率接近零)\n');
end

% 检查是否成功
if max_channel == expected_channel
    fprintf('✅ 成功: 信号出现在预期信道\n');
else
    fprintf('❌ 问题: 信号出现在信道 %d，预期在信道 %d\n', max_channel, expected_channel);
    fprintf('   可能原因: 频率偏移或信道分配理解有误\n');
end

fprintf('\n=== 测试完成 ===\n');
