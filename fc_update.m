function fc_10ms=fc_update(start_index,end_index,fc,run_nums)
run_times=run_nums-(length(fc)-start_index+1)-end_index;
times=run_times/length(fc);
if times<0
    if start_index<=end_index
        fc_10ms=fc(start_index:end_index);
    else
        fc_10ms=[fc(start_index:end),fc(1:end_index)];
    end
else
    repeat_onebeamfc=repmat(fc,1,times);
    fc_10ms=[fc(start_index:end),repeat_onebeamfc,fc(1:end_index)];
end
end