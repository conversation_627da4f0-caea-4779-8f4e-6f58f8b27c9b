clc;clear all;close all
load coef_lpf.mat;
fs = 200e6;
t = 0:1/fs:1e-6;
Fc1 = 10e6;  % 信号1 载频 10 MHz
Fc2 = 60e6;
Bw = 5e6;   % 线性调频信号带宽 16 MHz
T = 5e-6;   % 信号时长 10 us
N = T * fs;  % 采样点数
% 生成时间轴
t = (0:N-1) / fs;
% 生成线性调频信号（LFM）
k = Bw / T; % 线性调频斜率
s1 = (t < T) .* exp(1j * (2*pi*Fc1*t + pi*k*t.^2)); % 10 MHz LFM
s2 = (t < T) .* exp(1j * (2*pi*Fc2*t + pi*k*t.^2)); % 60 MHz LFM
sig = s1 + s2;
len = length(sig);
h = coef_lpf;
N = length(h);
D = 8;
y = zeros(D,len);
% PolyPhase Componets
i = 1:length(h);
h_channel = zeros(D,length(h));
im = sqrt(-1);
x_fre = linspace(0,fs,N);
for j = 1:D
    h_channel(j,:) = downsample(h.*exp((-im*2*pi*((j-1)*(i-1)))/D), 1);
    y(j,:) = filter(h_channel(j,:),1,sig);
end
figure()
for i = 1:D+1
    switch i
        case 1
            subplot (2,1,1);
            plot(x_fre,abs(fftshift(fft(h))));
            title('Orignal LPF ')
        otherwise
            subplot (2,1,2)
            plot(x_fre,abs(fft(h_channel(i-1,:))));
            hold on;
            title('channelize')
    end
end
x_fre1 = linspace(0,fs,len);
figure()
plot(x_fre1,abs(fft(sig)));
for iter = 1:D
    if mod(iter,4) ==1
        figure()
        j = 1;
    end
    subplot(4,1,j)
    j = j+1;
    plot(x_fre1,abs(fft(y(iter,:))));
    title(['信道',num2str(D - iter + 1)]);
end