clc;clear all;close all
load coef_lpf.mat;
fs = 200e6;
t = 0:1/fs:1e-6;
Fc1 = 30e6;  % 信号1 载频 30 MHz (适合4信道)
Fc2 = 90e6;  % 信号2 载频 90 MHz
Bw = 5e6;   % 线性调频信号带宽 5 MHz (减小带宽避免跨信道)
T = 5e-6;   % 信号时长 5 us
N = T * fs;  % 采样点数
% 生成时间轴
t = (0:N-1) / fs;
% 生成线性调频信号（LFM）
k = Bw / T; % 线性调频斜率
s1 = (t < T) .* exp(1j * (2*pi*Fc1*t + pi*k*t.^2)); % 30 MHz LFM
s2 = (t < T) .* exp(1j * (2*pi*Fc2*t + pi*k*t.^2)); % 90 MHz LFM
sig = s1 + s2;
len = length(sig);
h = coef_lpf;
N = length(h);
D = 4;  % 改为4信道
y = zeros(D,len);
% PolyPhase Componets
i = 1:length(h);
h_channel = zeros(D,length(h));
im = sqrt(-1);
x_fre = linspace(0,fs,N);
for j = 1:D
    h_channel(j,:) = downsample(h.*exp((-im*2*pi*((j-1)*(i-1)))/D), 1);
    y(j,:) = filter(h_channel(j,:),1,sig);
end

% 显示滤波器特性
figure('Name', '4信道滤波器特性')
for i = 1:D+1
    switch i
        case 1
            subplot (2,1,1);
            plot(x_fre/1e6,abs(fftshift(fft(h))));
            title('原始低通滤波器')
            xlabel('频率 (MHz)'); ylabel('幅度');
            grid on;
        otherwise
            subplot (2,1,2)
            plot(x_fre/1e6,abs(fft(h_channel(i-1,:))));
            hold on;
            title('4信道化滤波器组')
            xlabel('频率 (MHz)'); ylabel('幅度');
            grid on;
    end
end
legend(arrayfun(@(x) sprintf('信道%d', x), 1:D, 'UniformOutput', false));

% 显示输入信号频谱
x_fre1 = linspace(0,fs,len);
figure('Name', '输入信号频谱')
plot(x_fre1/1e6,abs(fft(sig)));
title('输入信号频谱 (30MHz + 90MHz LFM)');
xlabel('频率 (MHz)'); ylabel('幅度');
grid on;

% 显示各信道输出 (2x2布局适合4信道)
figure('Name', '4信道化输出')
for iter = 1:D
    subplot(2,2,iter)
    plot(x_fre1/1e6,abs(fft(y(iter,:))));
    title(['信道',num2str(D - iter + 1), ' (实际信道', num2str(iter), ')']);
    xlabel('频率 (MHz)'); ylabel('幅度');
    grid on;
    
    % 添加理论信道范围标识
    ylim_vals = ylim;
    hold on;
    channel_start = (iter-1) * (fs/D) / 1e6;
    channel_end = iter * (fs/D) / 1e6;
    plot([channel_start, channel_end], [ylim_vals(2)*0.9, ylim_vals(2)*0.9], 'r--', 'LineWidth', 2);
    text((channel_start + channel_end)/2, ylim_vals(2)*0.95, ...
        sprintf('%.0f-%.0fMHz', channel_start, channel_end), ...
        'HorizontalAlignment', 'center', 'Color', 'red', 'FontSize', 8);
end

% 功率分析
fprintf('=== 4信道功率分析 ===\n');
channel_power = zeros(1, D);
for i = 1:D
    channel_power(i) = sum(abs(y(i,:)).^2);
end

[max_power, max_channel] = max(channel_power);
fprintf('各信道功率分布:\n');
for i = 1:D
    power_db = 10*log10(channel_power(i)/max_power);
    display_channel = D - i + 1;
    fprintf('  信道%d (实际信道%d): %.1f dB', display_channel, i, power_db);
    if i == max_channel
        fprintf(' ← 最强');
    end
    fprintf('\n');
end

fprintf('\n预期结果:\n');
fprintf('  30 MHz信号应该主要出现在信道1或2\n');
fprintf('  90 MHz信号应该主要出现在信道2或3\n');
fprintf('  每信道理论带宽: %.0f MHz\n', fs/D/1e6);

% 功率分布图
figure('Name', '信道功率分布')
bar(1:D, 10*log10(channel_power/max_power));
title('各信道功率分布');
xlabel('信道号（实际处理顺序）'); ylabel('相对功率 (dB)');
grid on;
% 标记最强信道
hold on;
bar(max_channel, 10*log10(channel_power(max_channel)/max_power), 'r');
legend('信道功率', '最强信道');

% 添加频率标签
for i = 1:D
    channel_center = (i-0.5) * (fs/D) / 1e6;
    text(i, 10*log10(channel_power(i)/max_power) + 2, sprintf('%.0fMHz', channel_center), ...
        'HorizontalAlignment', 'center', 'FontSize', 8);
end
