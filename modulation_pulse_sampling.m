function [signal,len]=modulation_pulse_sampling(modulation_variance,fs,PW,frequency_list,B,Af,fcos,barker_num,fc1,fc2)
PW_max=max(PW);
% frequency_list=double(frequency_list);
signal_size_1=length(frequency_list);
signal_size_2=length(0:1/fs:PW_max-1/fs);
signal=zeros(signal_size_1,signal_size_2);
len=[];
for i=1:signal_size_1
    if modulation_variance==1
        t=0:1/fs:PW(i)-1/fs;
        signal_mod=lfm(PW(i),frequency_list(i),B,t);
        signal(i,1:length(signal_mod)) = signal_mod;
        len=[len;length(t)];
    elseif modulation_variance==2
        t=0:1/fs:PW(i)-1/fs;
        signal_mod=nlfm(frequency_list(i),Af,fcos,t);
        signal(i,1:length(signal_mod)) = signal_mod;
        len=[len;length(t)];
    elseif modulation_variance==3
        t=0:1/fs:PW(i)-1/fs;
        signal_mod=barkerp(frequency_list(i),barker_num,t);
        signal(i,1:length(signal_mod)) = [length(t),signal_mod];
        len=[len;length(t)];
    elseif modulation_variance==4
        t=0:1/fs:PW(i)-1/fs;
        signal_mod=barkerf(fc1,fc2,barker_num,t);
        signal(i,1:length(signal_mod)) = signal_mod;
        len=[len;length(t)];
    else
        t=0:1/fs:PW(i)-1/fs;
        signal_mod=cw(frequency_list(i),t);
        signal(i,1:length(signal_mod)) = signal_mod;
        len=[len;length(t)];
    end
end