%% 分析数字信道化接收机的工作原理
clc; clear all; close all;

%% 1. 理解信道化算法
fprintf('=== 分析信道化算法 ===\n');

% 接收机参数
fs = 200e6;        % 采样率 200 MHz
D = 8;             % 8通道
load coef_lpf.mat;
h = coef_lpf;

% 分析多相滤波器
h_channel = zeros(D, length(h));
for j = 1:D
    h_channel(j,:) = downsample(h.*exp((-1j*2*pi*((j-1)*(0:length(h)-1)))/D), 1);
end

% 分析各信道的频率响应
N_fft = 2048;
f = linspace(0, fs, N_fft);
H_channel = zeros(D, N_fft);

for j = 1:D
    H_temp = fft(h_channel(j,:), N_fft);
    H_channel(j,:) = abs(H_temp);
end

% 找到每个信道的中心频率
channel_center_freq = zeros(1, D);
for j = 1:D
    [~, max_idx] = max(H_channel(j,:));
    channel_center_freq(j) = f(max_idx);
end

fprintf('各信道中心频率:\n');
for j = 1:D
    fprintf('  信道 %d: %.1f MHz\n', j, channel_center_freq(j)/1e6);
end

%% 2. 测试不同频率的信号
fprintf('\n=== 测试不同频率信号 ===\n');

test_frequencies = [10, 30, 50, 70, 90, 110, 130, 150] * 1e6;  % MHz
T = 5e-6;
N = round(T * fs);
t = (0:N-1) / fs;

results = zeros(length(test_frequencies), D);

for freq_idx = 1:length(test_frequencies)
    fc = test_frequencies(freq_idx);
    
    % 生成测试信号
    sig = exp(1j * 2*pi * fc * t);
    
    % 信道化处理
    y = zeros(D, length(sig));
    for j = 1:D
        y(j,:) = filter(h_channel(j,:), 1, sig);
    end
    
    % 计算各信道功率
    for j = 1:D
        results(freq_idx, j) = sum(abs(y(j,:)).^2);
    end
    
    % 找到最强信道
    [~, max_channel] = max(results(freq_idx, :));
    fprintf('%.0f MHz -> 信道 %d\n', fc/1e6, max_channel);
end

%% 3. 可视化信道特性
figure('Name', '信道化接收机特性分析');

% 子图1: 各信道频率响应
subplot(2,2,1);
for j = 1:D
    plot(f/1e6, 20*log10(H_channel(j,:) + eps));
    hold on;
end
title('各信道频率响应');
xlabel('频率 (MHz)'); ylabel('幅度 (dB)');
legend(arrayfun(@(x) sprintf('信道%d', x), 1:D, 'UniformOutput', false));
grid on;

% 子图2: 信道中心频率分布
subplot(2,2,2);
stem(1:D, channel_center_freq/1e6, 'filled');
title('各信道中心频率');
xlabel('信道号'); ylabel('中心频率 (MHz)');
grid on;

% 子图3: 频率-信道映射热图
subplot(2,2,3);
imagesc(1:D, test_frequencies/1e6, 10*log10(results + eps));
colorbar;
title('频率-信道功率映射');
xlabel('信道号'); ylabel('输入频率 (MHz)');

% 子图4: 30MHz信号的信道分布
subplot(2,2,4);
freq_30_idx = find(test_frequencies == 30e6);
if ~isempty(freq_30_idx)
    bar(1:D, 10*log10(results(freq_30_idx, :) / max(results(freq_30_idx, :))));
    title('30 MHz信号在各信道的功率分布');
    xlabel('信道号'); ylabel('相对功率 (dB)');
    grid on;
end

%% 4. 分析原始rec.m的信道编号
fprintf('\n=== 原始rec.m的信道编号分析 ===\n');
fprintf('注意: rec.m中显示的信道编号是倒序的!\n');
fprintf('代码: title([''信道'',num2str(D - iter + 1)]);\n');
fprintf('这意味着:\n');
for iter = 1:D
    display_channel = D - iter + 1;
    fprintf('  处理信道 %d -> 显示为 "信道%d"\n', iter, display_channel);
end

%% 5. 重新分析30MHz信号
fprintf('\n=== 重新分析30MHz信号结果 ===\n');
freq_30_idx = find(test_frequencies == 30e6);
if ~isempty(freq_30_idx)
    [~, actual_channel] = max(results(freq_30_idx, :));
    display_channel = D - actual_channel + 1;
    
    fprintf('30 MHz信号:\n');
    fprintf('  实际处理信道: %d\n', actual_channel);
    fprintf('  rec.m中显示为: "信道%d"\n', display_channel);
    fprintf('  对应频率范围: %.1f MHz附近\n', channel_center_freq(actual_channel)/1e6);
end

%% 6. 给出正确的信道分配理解
fprintf('\n=== 正确的信道分配 ===\n');
fprintf('基于多相滤波器的数字信道化:\n');
for j = 1:D
    fprintf('  信道 %d: 中心频率 %.1f MHz\n', j, channel_center_freq(j)/1e6);
end

fprintf('\n=== 分析完成 ===\n');
