function [IF_signal, IF_fc, t_if] = downconvert_rf_to_if(RF_pulse, RF_fc, RF_fs, target_IF_fc, IF_fs)
% 射频到中频下变频函数
% 输入:
%   RF_pulse: 射频脉冲信号 (复数)
%   RF_fc: 射频载频 (Hz)
%   RF_fs: 射频采样率 (Hz) 
%   target_IF_fc: 目标中频载频 (Hz)
%   IF_fs: 中频采样率 (Hz)
% 输出:
%   IF_signal: 中频信号
%   IF_fc: 实际中频载频
%   t_if: 中频时间轴

%% 参数设置
if nargin < 4
    target_IF_fc = 30e6;  % 默认中频30MHz
end
if nargin < 5
    IF_fs = 200e6;        % 默认中频采样率200MHz
end

%% 计算本振频率
LO_freq = RF_fc - target_IF_fc;  % 本振频率
IF_fc = target_IF_fc;            % 实际中频

%% 生成时间轴
N_rf = length(RF_pulse);
t_rf = (0:N_rf-1) / RF_fs;

%% 下变频混频
% 生成本振信号
LO_signal = exp(-1j * 2 * pi * LO_freq * t_rf);
% 混频
IF_signal_temp = RF_pulse .* LO_signal;

%% 低通滤波 (抗混叠)
% 设计低通滤波器
cutoff_freq = IF_fs / 2 * 0.8;  % 截止频率为奈奎斯特频率的80%
filter_order = 64;
lpf = fir1(filter_order, cutoff_freq/(RF_fs/2), 'low');
% 滤波
IF_signal_filtered = filter(lpf, 1, IF_signal_temp);

%% 重采样到中频采样率
if RF_fs ~= IF_fs
    % 计算重采样比例
    [P, Q] = rat(IF_fs / RF_fs, 1e-6);
    % 确保P和Q是整数
    P = round(P);
    Q = round(Q);
    % 重采样
    IF_signal = resample(IF_signal_filtered, P, Q);
else
    IF_signal = IF_signal_filtered;
end

%% 生成中频时间轴
N_if = length(IF_signal);
t_if = (0:N_if-1) / IF_fs;

%% 显示信息
fprintf('下变频完成:\n');
fprintf('  射频载频: %.3f GHz\n', RF_fc/1e9);
fprintf('  本振频率: %.3f GHz\n', LO_freq/1e9);
fprintf('  中频载频: %.1f MHz\n', IF_fc/1e6);
fprintf('  射频采样率: %.1f MHz\n', RF_fs/1e6);
fprintf('  中频采样率: %.1f MHz\n', IF_fs/1e6);

end
