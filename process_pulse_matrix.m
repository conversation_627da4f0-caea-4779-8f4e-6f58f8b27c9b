%% 直接处理脉冲矩阵的数字信道化接收机测试
clc; clear all; close all;

%% 1. 运行雷达仿真获取脉冲串
fprintf('=== 运行雷达仿真 ===\n');
main;  % 运行主仿真程序

if ~exist('IF_Signals', 'var')
    error('未找到IF_Signals变量，请确保main.m正常运行');
end

fprintf('仿真完成，获得脉冲串:\n');
fprintf('  脉冲数量: %d\n', size(IF_Signals, 1));
fprintf('  每脉冲最大采样点: %d\n', size(IF_Signals, 2));

% 检查关键变量是否存在
fprintf('\n变量检查:\n');
if exist('Signal_lenlist', 'var')
    fprintf('  Signal_lenlist: 存在，长度 %d\n', length(Signal_lenlist));
else
    fprintf('  Signal_lenlist: 不存在，将自动检测脉冲长度\n');
end

if exist('fc_10ms', 'var')
    fprintf('  fc_10ms: 存在，长度 %d\n', length(fc_10ms));
else
    fprintf('  fc_10ms: 不存在，将使用默认载频\n');
end

if exist('TOA_list', 'var')
    fprintf('  TOA_list: 存在，长度 %d\n', length(TOA_list));
else
    fprintf('  TOA_list: 不存在\n');
end

%% 2. 选择处理方式
fprintf('\n=== 脉冲串处理选择 ===\n');
fprintf('1. 逐个脉冲处理（分析每个脉冲的信道化结果）\n');
fprintf('2. 批量处理（统计分析）\n');

% 选择处理的脉冲数量
max_pulses = min(20, size(IF_Signals, 1));
fprintf('处理脉冲数量: %d\n', max_pulses);

%% 3. 接收机配置
fprintf('\n=== 接收机配置 ===\n');
load coef_lpf.mat;
h = coef_lpf;
D = 4;  % 2信道配置
IF_fs = 200e6;
target_IF_fc = 75e6;  % 目标中频75MHz

% 信道化滤波器
h_channel = zeros(D, length(h));
for j = 1:D
    h_channel(j,:) = downsample(h.*exp((-1j*2*pi*((j-1)*(0:length(h)-1)))/D), 1);
end

channel_center_freqs = [10.9, 89.1];  % MHz
fprintf('接收机配置:\n');
fprintf('  信道数: %d\n', D);
fprintf('  目标中频: %.1f MHz\n', target_IF_fc/1e6);
fprintf('  信道中心频率: [%.1f, %.1f] MHz\n', channel_center_freqs);

%% 4. 逐个脉冲处理
fprintf('\n=== 逐个脉冲处理 ===\n');

pulse_results = [];
all_channel_outputs = [];

for pulse_idx = 1:max_pulses
    % 提取当前脉冲
    % 检查Signal_lenlist是否存在，如果不存在则使用非零元素长度
    if exist('Signal_lenlist', 'var') && length(Signal_lenlist) >= pulse_idx
        pulse_length = Signal_lenlist(pulse_idx);
    else
        % 自动检测脉冲长度（找到非零元素的范围）
        pulse_data = IF_Signals(pulse_idx, :);
        nonzero_indices = find(pulse_data ~= 0);
        if ~isempty(nonzero_indices)
            pulse_length = nonzero_indices(end);
        else
            pulse_length = size(IF_Signals, 2);  % 使用全长度
        end
    end

    RF_pulse = IF_Signals(pulse_idx, 1:pulse_length);

    % 检查fc_10ms是否存在
    if exist('fc_10ms', 'var') && length(fc_10ms) >= pulse_idx
        RF_fc = fc_10ms(pulse_idx);
    else
        % 使用默认载频或从配置中获取
        if exist('IF_BP', 'var') && isfield(IF_BP, 'fc_list')
            RF_fc = IF_BP.fc_list(mod(pulse_idx-1, length(IF_BP.fc_list)) + 1);
        else
            RF_fc = 8e9;  % 默认8GHz
        end
    end
    
    % 转换为复数并下变频
    RF_pulse_complex = hilbert(RF_pulse);
    [IF_pulse, ~, ~] = downconvert_rf_to_if(RF_pulse_complex, RF_fc, IF_BP.fs, target_IF_fc, IF_fs);
    
    % 调整长度
    T_rec = 5e-6;
    N_rec = round(T_rec * IF_fs);
    if length(IF_pulse) < N_rec
        sig_for_rec = [IF_pulse, zeros(1, N_rec - length(IF_pulse))];
    else
        sig_for_rec = IF_pulse(1:N_rec);
    end
    
    % 信道化处理
    y = zeros(D, length(sig_for_rec));
    for j = 1:D
        y(j,:) = filter(h_channel(j,:), 1, sig_for_rec);
    end
    
    % 计算功率
    channel_power = zeros(1, D);
    for i = 1:D
        channel_power(i) = sum(abs(y(i,:)).^2);
    end
    
    [max_power, max_channel] = max(channel_power);
    
    % 存储结果
    pulse_results = [pulse_results; struct(...
        'pulse_idx', pulse_idx, ...
        'rf_freq', RF_fc, ...
        'if_freq', target_IF_fc, ...
        'max_channel', max_channel, ...
        'channel_power', channel_power, ...
        'power_ratio', channel_power/max_power)];
    
    % 存储信道输出（用于后续分析）
    if pulse_idx <= 5  % 只存储前5个脉冲的详细输出
        all_channel_outputs = [all_channel_outputs; y];
    end
    
    if mod(pulse_idx, 5) == 0
        fprintf('已处理 %d/%d 个脉冲\n', pulse_idx, max_pulses);
    end
end

%% 5. 统计分析
fprintf('\n=== 统计分析 ===\n');

% 信道使用统计
channel_usage = zeros(1, D);
for i = 1:length(pulse_results)
    channel_usage(pulse_results(i).max_channel) = channel_usage(pulse_results(i).max_channel) + 1;
end

fprintf('信道使用统计:\n');
for i = 1:D
    usage_percent = channel_usage(i) / max_pulses * 100;
    fprintf('  信道 %d: %d次 (%.1f%%)\n', i, channel_usage(i), usage_percent);
end

% 载频分布分析
rf_freqs = [pulse_results.rf_freq];
unique_rf_freqs = unique(rf_freqs);
fprintf('\n载频分布:\n');
fprintf('  不同载频数量: %d\n', length(unique_rf_freqs));
fprintf('  载频范围: %.3f - %.3f GHz\n', min(unique_rf_freqs)/1e9, max(unique_rf_freqs)/1e9);

% 载频与信道的对应关系
fprintf('\n载频-信道对应关系:\n');
for freq = unique_rf_freqs(1:min(10, length(unique_rf_freqs)))  % 显示前10个
    freq_indices = find(rf_freqs == freq);
    channels_used = [pulse_results(freq_indices).max_channel];
    dominant_channel = mode(channels_used);
    fprintf('  %.3f GHz -> 主要使用信道 %d\n', freq/1e9, dominant_channel);
end

%% 6. 可视化结果
fprintf('\n=== 生成图形 ===\n');

% 图1: 脉冲串概览
figure('Name', '脉冲串处理概览');

subplot(2,3,1);
stem(1:max_pulses, rf_freqs/1e9, 'filled');
title('脉冲载频序列');
xlabel('脉冲编号'); ylabel('载频 (GHz)');
grid on;

subplot(2,3,2);
max_channels = [pulse_results.max_channel];
stem(1:max_pulses, max_channels, 'filled');
title('最强信道分布');
xlabel('脉冲编号'); ylabel('信道号');
ylim([0.5, D+0.5]);
grid on;

subplot(2,3,3);
bar(1:D, channel_usage);
title('信道使用统计');
xlabel('信道号'); ylabel('使用次数');
grid on;

subplot(2,3,4);
% 功率分布热图
power_matrix = zeros(max_pulses, D);
for i = 1:max_pulses
    power_matrix(i, :) = 10*log10(pulse_results(i).channel_power / max(pulse_results(i).channel_power));
end
imagesc(1:D, 1:max_pulses, power_matrix);
colorbar;
title('各脉冲信道功率分布 (dB)');
xlabel('信道号'); ylabel('脉冲编号');

subplot(2,3,5);
% 载频 vs 信道散点图
scatter(rf_freqs/1e9, max_channels, 50, 'filled');
title('载频 vs 最强信道');
xlabel('载频 (GHz)'); ylabel('信道号');
ylim([0.5, D+0.5]);
grid on;

subplot(2,3,6);
% 跨信道分析
cross_channel_ratio = zeros(1, max_pulses);
for i = 1:max_pulses
    powers = pulse_results(i).channel_power;
    max_power = max(powers);
    other_power = sum(powers) - max_power;
    cross_channel_ratio(i) = other_power / max_power;
end
plot(1:max_pulses, cross_channel_ratio);
title('跨信道程度');
xlabel('脉冲编号'); ylabel('跨信道比例');
grid on;

% 图2: 详细信道输出分析（前5个脉冲）
if size(all_channel_outputs, 1) >= D*5
    figure('Name', '前5个脉冲的信道化输出');
    
    for pulse_idx = 1:5
        for ch = 1:D
            subplot(5, D, (pulse_idx-1)*D + ch);
            
            row_idx = (pulse_idx-1)*D + ch;
            if row_idx <= size(all_channel_outputs, 1)
                x_fre = linspace(0, IF_fs, size(all_channel_outputs, 2));
                plot(x_fre/1e6, abs(fft(all_channel_outputs(row_idx, :))));
                
                title(sprintf('脉冲%d-信道%d (%.3fGHz)', pulse_idx, ch, pulse_results(pulse_idx).rf_freq/1e9));
                xlabel('频率 (MHz)'); ylabel('幅度');
                grid on;
            end
        end
    end
end

% 图3: 统计分析图
figure('Name', '脉冲串统计分析');

subplot(2,2,1);
histogram(rf_freqs/1e9, 20);
title('载频分布直方图');
xlabel('载频 (GHz)'); ylabel('脉冲数量');
grid on;

subplot(2,2,2);
histogram(max_channels, 1:D+1);
title('信道使用分布');
xlabel('信道号'); ylabel('脉冲数量');
grid on;

subplot(2,2,3);
histogram(cross_channel_ratio, 20);
title('跨信道程度分布');
xlabel('跨信道比例'); ylabel('脉冲数量');
grid on;

subplot(2,2,4);
% 信道功率随时间变化
for ch = 1:D
    channel_powers = zeros(1, max_pulses);
    for i = 1:max_pulses
        channel_powers(i) = pulse_results(i).channel_power(ch);
    end
    plot(1:max_pulses, 10*log10(channel_powers/max(channel_powers(:))));
    hold on;
end
title('各信道功率时间变化');
xlabel('脉冲编号'); ylabel('相对功率 (dB)');
legend(arrayfun(@(x) sprintf('信道%d', x), 1:D, 'UniformOutput', false));
grid on;

%% 7. 性能评估
fprintf('\n=== 性能评估 ===\n');

% 信道平衡度
channel_balance = std(channel_usage) / mean(channel_usage);
fprintf('信道平衡度: %.3f (越小越平衡)\n', channel_balance);

% 平均跨信道程度
avg_cross_channel = mean(cross_channel_ratio);
fprintf('平均跨信道程度: %.3f\n', avg_cross_channel);

% 载频覆盖度
freq_coverage = length(unique_rf_freqs) / length(IF_BP.fc_list) * 100;
fprintf('载频覆盖度: %.1f%%\n', freq_coverage);

fprintf('\n=== 脉冲串处理完成 ===\n');
fprintf('🎉 成功处理 %d 个脉冲的完整脉冲串！\n', max_pulses);
fprintf('📊 可以观察跳频雷达在接收机中的完整表现\n');
