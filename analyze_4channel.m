%% 分析4信道数字信道化接收机的工作原理
clc; clear all; close all;

fprintf('=== 4信道数字信道化接收机分析 ===\n');

%% 1. 理解4信道化算法
% 接收机参数
fs = 200e6;        % 采样率 200 MHz
D = 4;             % 4通道
load coef_lpf.mat;
h = coef_lpf;

% 分析多相滤波器
h_channel = zeros(D, length(h));
for j = 1:D
    h_channel(j,:) = downsample(h.*exp((-1j*2*pi*((j-1)*(0:length(h)-1)))/D), 1);
end

% 分析各信道的频率响应
N_fft = 2048;
f = linspace(0, fs, N_fft);
H_channel = zeros(D, N_fft);

for j = 1:D
    H_temp = fft(h_channel(j,:), N_fft);
    H_channel(j,:) = abs(H_temp);
end

% 找到每个信道的中心频率
channel_center_freq = zeros(1, D);
channel_bandwidth = zeros(1, D);

for j = 1:D
    [max_val, max_idx] = max(H_channel(j,:));
    channel_center_freq(j) = f(max_idx);
    
    % 计算3dB带宽
    half_max = max_val / sqrt(2);
    indices = find(H_channel(j,:) >= half_max);
    if ~isempty(indices)
        channel_bandwidth(j) = (f(indices(end)) - f(indices(1)));
    end
end

fprintf('4信道特性分析:\n');
fprintf('理论每信道带宽: %.1f MHz\n', fs/D/1e6);
for j = 1:D
    fprintf('  信道 %d: 中心频率 %.1f MHz, 3dB带宽 %.1f MHz\n', ...
        j, channel_center_freq(j)/1e6, channel_bandwidth(j)/1e6);
end

%% 2. 测试不同频率的信号
fprintf('\n=== 测试不同频率信号 ===\n');

% 测试频率覆盖整个200MHz带宽
test_frequencies = [10, 25, 40, 60, 75, 90, 110, 125, 140, 160, 175, 190] * 1e6;  % MHz
T = 5e-6;
N = round(T * fs);
t = (0:N-1) / fs;

results = zeros(length(test_frequencies), D);

for freq_idx = 1:length(test_frequencies)
    fc = test_frequencies(freq_idx);
    
    % 生成测试信号
    sig = exp(1j * 2*pi * fc * t);
    
    % 信道化处理
    y = zeros(D, length(sig));
    for j = 1:D
        y(j,:) = filter(h_channel(j,:), 1, sig);
    end
    
    % 计算各信道功率
    for j = 1:D
        results(freq_idx, j) = sum(abs(y(j,:)).^2);
    end
    
    % 找到最强信道
    [~, max_channel] = max(results(freq_idx, :));
    fprintf('%.0f MHz -> 信道 %d\n', fc/1e6, max_channel);
end

%% 3. 特别测试雷达相关频率
fprintf('\n=== 雷达频率测试 ===\n');
radar_frequencies = [10, 30, 50] * 1e6;  % 常用的中频

for fc = radar_frequencies
    % 生成LFM信号（模拟雷达脉冲）
    PW = 1e-6;  % 1us脉宽
    B = 30e6;   % 30MHz带宽
    N_pulse = round(PW * fs);
    t_pulse = (0:N_pulse-1) / fs;
    k = B / PW;
    
    % LFM信号
    lfm_sig = exp(1j * (2*pi*fc*t_pulse + pi*k*t_pulse.^2));
    
    % 扩展到5us
    sig_extended = [lfm_sig, zeros(1, N - length(lfm_sig))];
    
    % 信道化
    y = zeros(D, length(sig_extended));
    for j = 1:D
        y(j,:) = filter(h_channel(j,:), 1, sig_extended);
    end
    
    % 分析结果
    power = zeros(1, D);
    for j = 1:D
        power(j) = sum(abs(y(j,:)).^2);
    end
    
    [max_power, max_channel] = max(power);
    display_channel = D - max_channel + 1;  % rec.m风格
    
    fprintf('%.0f MHz LFM (30MHz带宽):\n', fc/1e6);
    fprintf('  最强信道: %d (rec.m显示: "信道%d")\n', max_channel, display_channel);
    
    % 显示功率分布
    fprintf('  功率分布: ');
    for j = 1:D
        fprintf('信道%d:%.1fdB ', j, 10*log10(power(j)/max_power));
    end
    fprintf('\n');
end

%% 4. 可视化结果
figure('Name', '4信道接收机特性分析');

% 子图1: 各信道频率响应
subplot(2,2,1);
for j = 1:D
    plot(f/1e6, 20*log10(H_channel(j,:) + eps));
    hold on;
end
title('4信道频率响应');
xlabel('频率 (MHz)'); ylabel('幅度 (dB)');
legend(arrayfun(@(x) sprintf('信道%d', x), 1:D, 'UniformOutput', false));
grid on;
xlim([0, 200]);

% 子图2: 信道中心频率和带宽
subplot(2,2,2);
bar(1:D, channel_center_freq/1e6);
title('各信道中心频率');
xlabel('信道号'); ylabel('中心频率 (MHz)');
grid on;
% 添加带宽信息
for j = 1:D
    text(j, channel_center_freq(j)/1e6 + 5, sprintf('BW:%.0fMHz', channel_bandwidth(j)/1e6), ...
        'HorizontalAlignment', 'center', 'FontSize', 8);
end

% 子图3: 频率-信道映射热图
subplot(2,2,3);
imagesc(1:D, test_frequencies/1e6, 10*log10(results + eps));
colorbar;
title('频率-信道功率映射');
xlabel('信道号'); ylabel('输入频率 (MHz)');
colormap('jet');

% 子图4: 理论vs实际信道分配
subplot(2,2,4);
theoretical_centers = [25, 75, 125, 175];  % 理论中心频率
actual_centers = channel_center_freq/1e6;

bar_width = 0.35;
x = 1:D;
bar(x - bar_width/2, theoretical_centers, bar_width, 'b', 'DisplayName', '理论');
hold on;
bar(x + bar_width/2, actual_centers, bar_width, 'r', 'DisplayName', '实际');
title('理论 vs 实际信道中心频率');
xlabel('信道号'); ylabel('中心频率 (MHz)');
legend();
grid on;

%% 5. 输出4信道的实际参数
fprintf('\n=== 4信道实际参数 ===\n');
fprintf('用于extract_pulse_from_simulation.m的参数:\n');
fprintf('channel_center_freqs = [');
for j = 1:D
    fprintf('%.1f', channel_center_freq(j)/1e6);
    if j < D
        fprintf(', ');
    end
end
fprintf('];  %% MHz\n');

%% 6. 建议
fprintf('\n=== 使用建议 ===\n');
fprintf('1. 4信道配置优势:\n');
fprintf('   - 每信道带宽: ~%.0f MHz\n', mean(channel_bandwidth)/1e6);
fprintf('   - 雷达信号带宽: 30 MHz\n');
fprintf('   - 避免跨信道问题: ✅\n');
fprintf('\n2. 推荐的中频选择:\n');
for j = 1:D
    freq_range = sprintf('%.0f-%.0f MHz', ...
        channel_center_freq(j)/1e6 - channel_bandwidth(j)/2e6, ...
        channel_center_freq(j)/1e6 + channel_bandwidth(j)/2e6);
    fprintf('   信道%d范围: %s\n', j, freq_range);
end

fprintf('\n=== 4信道分析完成 ===\n');
