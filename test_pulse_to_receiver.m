%% 测试脚本：提取射频脉冲并送入数字信道化接收机
clc; clear all; close all;

%% 1. 运行雷达仿真，获取射频脉冲
fprintf('=== 步骤1: 运行雷达仿真 ===\n');
% 运行main.m获取IF_Signals (这里假设已经运行过)
load('IF_BP_1.mat');  % 加载配置参数

% 模拟运行main.m的关键部分来获取一个脉冲
% 这里简化处理，直接生成一个LFM脉冲作为示例
fs_rf = IF_BP.fs;  % 射频采样率 2.4 GHz
fc_rf = IF_BP.fc_list(1);  % 第一个载频 8.0 GHz
PW = 1e-6;  % 脉宽 1us
B = IF_BP.B;  % 带宽 30 MHz

% 生成射频LFM脉冲
t_rf = 0:1/fs_rf:PW-1/fs_rf;
k = B / PW;  % 调频斜率
RF_pulse = exp(1j * (2*pi*fc_rf*t_rf + pi*k*t_rf.^2));

fprintf('射频脉冲参数:\n');
fprintf('  载频: %.3f GHz\n', fc_rf/1e9);
fprintf('  脉宽: %.1f us\n', PW*1e6);
fprintf('  带宽: %.1f MHz\n', B/1e6);
fprintf('  采样率: %.1f GHz\n', fs_rf/1e9);
fprintf('  采样点数: %d\n', length(RF_pulse));

%% 2. 下变频到中频
fprintf('\n=== 步骤2: 射频下变频到中频 ===\n');
target_IF_fc = 30e6;  % 目标中频 30 MHz
IF_fs = 200e6;        % 中频采样率 200 MHz

[IF_pulse, IF_fc, t_if] = downconvert_rf_to_if(RF_pulse, fc_rf, fs_rf, target_IF_fc, IF_fs);

%% 3. 适配接收机格式
fprintf('\n=== 步骤3: 适配接收机输入格式 ===\n');
% rec.m期望的信号长度和格式
T_rec = 5e-6;  % 接收机期望的信号时长
N_rec = T_rec * IF_fs;  % 接收机期望的采样点数

% 调整信号长度
if length(IF_pulse) < N_rec
    % 如果信号太短，用零填充
    sig_for_rec = [IF_pulse, zeros(1, N_rec - length(IF_pulse))];
    fprintf('信号长度不足，用零填充到 %d 点\n', N_rec);
elseif length(IF_pulse) > N_rec
    % 如果信号太长，截取前面部分
    sig_for_rec = IF_pulse(1:N_rec);
    fprintf('信号长度过长，截取前 %d 点\n', N_rec);
else
    sig_for_rec = IF_pulse;
    fprintf('信号长度正好匹配\n');
end

%% 4. 运行数字信道化接收机
fprintf('\n=== 步骤4: 运行数字信道化接收机 ===\n');
% 加载接收机参数
load coef_lpf.mat;
h = coef_lpf;
D = 8;  % 8通道
len = length(sig_for_rec);
y = zeros(D, len);

% 多相滤波器组实现信道化
h_channel = zeros(D, length(h));
im = sqrt(-1);
for j = 1:D
    h_channel(j,:) = downsample(h.*exp((-im*2*pi*((j-1)*(0:length(h)-1)))/D), 1);
    y(j,:) = filter(h_channel(j,:), 1, sig_for_rec);
end

fprintf('信道化完成，共 %d 个信道\n', D);

%% 5. 结果分析和显示
fprintf('\n=== 步骤5: 结果分析 ===\n');

% 频率轴
x_fre = linspace(0, IF_fs, length(h));
x_fre1 = linspace(0, IF_fs, len);

% 显示原始滤波器和信道化滤波器
figure('Name', '滤波器特性');
subplot(2,1,1);
plot(x_fre/1e6, abs(fftshift(fft(h))));
title('原始低通滤波器');
xlabel('频率 (MHz)'); ylabel('幅度');

subplot(2,1,2);
for i = 1:D
    plot(x_fre/1e6, abs(fft(h_channel(i,:))));
    hold on;
end
title('信道化滤波器组');
xlabel('频率 (MHz)'); ylabel('幅度');
legend(arrayfun(@(x) sprintf('信道%d', x), 1:D, 'UniformOutput', false));

% 显示输入信号频谱
figure('Name', '输入信号频谱');
plot(x_fre1/1e6, abs(fft(sig_for_rec)));
title('输入中频信号频谱');
xlabel('频率 (MHz)'); ylabel('幅度');
grid on;

% 显示各信道输出
figure('Name', '信道化输出');
for iter = 1:D
    subplot(4, 2, iter);
    plot(x_fre1/1e6, abs(fft(y(iter,:))));
    title(['信道 ', num2str(D - iter + 1)]);
    xlabel('频率 (MHz)'); ylabel('幅度');
    grid on;
end

% 计算信号在哪个信道
expected_channel = round(IF_fc / (IF_fs/D)) + 1;
fprintf('预期信号应该出现在信道 %d (中频 %.1f MHz)\n', expected_channel, IF_fc/1e6);

%% 6. 性能评估
fprintf('\n=== 步骤6: 性能评估 ===\n');
% 计算各信道的信号功率
channel_power = zeros(1, D);
for i = 1:D
    channel_power(i) = sum(abs(y(i,:)).^2);
end

[max_power, max_channel] = max(channel_power);
fprintf('最大功率出现在信道 %d\n', max_channel);
fprintf('信道功率分布:\n');
for i = 1:D
    fprintf('  信道 %d: %.2f dB\n', i, 10*log10(channel_power(i)/max_power));
end

fprintf('\n=== 测试完成 ===\n');
fprintf('射频脉冲成功转换为中频并通过数字信道化接收机处理！\n');
