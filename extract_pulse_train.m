%% 处理完整脉冲串的数字信道化接收机测试
clc; clear all; close all;

%% 1. 运行雷达仿真获取脉冲串
fprintf('=== 运行雷达仿真 ===\n');
main;  % 运行主仿真程序

% 检查是否成功生成了IF_Signals
if ~exist('IF_Signals', 'var')
    error('未找到IF_Signals变量，请确保main.m正常运行');
end

fprintf('仿真完成，获得脉冲串:\n');
fprintf('  脉冲数量: %d\n', size(IF_Signals, 1));
fprintf('  每脉冲最大采样点: %d\n', size(IF_Signals, 2));
fprintf('  载频数量: %d\n', length(fc_10ms));
fprintf('  TOA时间跨度: %.2f ms\n', (TOA_list(end)-TOA_list(1))*1000);

%% 2. 构建连续的脉冲串信号
fprintf('\n=== 构建连续脉冲串 ===\n');

% 选择处理的脉冲数量（避免信号过长）
max_pulses = min(50, size(IF_Signals, 1));  % 最多处理50个脉冲
fprintf('处理脉冲数量: %d\n', max_pulses);

% 计算脉冲间隔（基于TOA）
if length(TOA_list) >= max_pulses
    pulse_intervals = diff(TOA_list(1:max_pulses));  % 脉冲间隔时间
else
    pulse_intervals = diff(TOA_list);
    max_pulses = length(TOA_list);
end

% 转换为采样点间隔
interval_samples = round(pulse_intervals * IF_BP.fs);

% 构建连续信号
total_samples = sum(Signal_lenlist(1:max_pulses)) + sum(interval_samples);
pulse_train = zeros(1, total_samples);
pulse_info = [];  % 存储每个脉冲的信息

current_pos = 1;
for i = 1:max_pulses
    % 获取当前脉冲
    pulse_length = Signal_lenlist(i);
    pulse_data = IF_Signals(i, 1:pulse_length);
    
    % 放置脉冲
    pulse_train(current_pos:current_pos+pulse_length-1) = pulse_data;
    
    % 记录脉冲信息
    pulse_info = [pulse_info; struct('index', i, 'start', current_pos, ...
        'length', pulse_length, 'fc', fc_10ms(i), 'toa', TOA_list(i))];
    
    % 更新位置（脉冲 + 间隔）
    current_pos = current_pos + pulse_length;
    if i < max_pulses
        current_pos = current_pos + interval_samples(i);
    end
end

fprintf('连续脉冲串构建完成:\n');
fprintf('  总长度: %d 采样点\n', length(pulse_train));
fprintf('  时间长度: %.2f ms\n', length(pulse_train)/IF_BP.fs*1000);
fprintf('  载频范围: %.3f - %.3f GHz\n', min(fc_10ms(1:max_pulses))/1e9, max(fc_10ms(1:max_pulses))/1e9);

%% 3. 转换为复数信号并下变频
fprintf('\n=== 下变频处理 ===\n');

% 转换为复数信号
pulse_train_complex = hilbert(pulse_train);

% 选择中频（基于载频范围的中心）
mean_rf_freq = mean(fc_10ms(1:max_pulses));
if mean_rf_freq > 5e9
    target_IF_fc = 75e6;   % 使用75MHz中频（2信道最佳）
else
    target_IF_fc = 39e6;   % 使用39MHz中频
end

IF_fs = 200e6;  % 中频采样率

fprintf('脉冲串下变频配置:\n');
fprintf('  平均射频载频: %.3f GHz\n', mean_rf_freq/1e9);
fprintf('  目标中频: %.1f MHz\n', target_IF_fc/1e6);

% 下变频（使用平均载频作为参考）
[IF_pulse_train, IF_fc, t_if] = downconvert_rf_to_if(pulse_train_complex, mean_rf_freq, IF_BP.fs, target_IF_fc, IF_fs);

%% 4. 数字信道化接收机处理
fprintf('\n=== 数字信道化接收机处理 ===\n');

% 接收机配置
load coef_lpf.mat;
h = coef_lpf;
D = 2;  % 2信道配置（适合50MHz带宽）

fprintf('接收机配置:\n');
fprintf('  信道数: %d\n', D);
fprintf('  理论每信道带宽: %.1f MHz\n', IF_fs/D/1e6);

% 信道化处理
len = length(IF_pulse_train);
y = zeros(D, len);
h_channel = zeros(D, length(h));

for j = 1:D
    h_channel(j,:) = downsample(h.*exp((-1j*2*pi*((j-1)*(0:length(h)-1)))/D), 1);
    y(j,:) = filter(h_channel(j,:), 1, IF_pulse_train);
end

fprintf('信道化处理完成\n');

%% 5. 分析结果
fprintf('\n=== 脉冲串分析 ===\n');

% 计算各信道的总功率
channel_power = zeros(1, D);
for i = 1:D
    channel_power(i) = sum(abs(y(i,:)).^2);
end

[max_power, max_channel] = max(channel_power);
channel_center_freqs = [10.9, 89.1];  % 2信道中心频率

fprintf('整体功率分布:\n');
for i = 1:D
    power_db = 10*log10(channel_power(i)/max_power);
    fprintf('  信道 %d (中心: %.1f MHz): %.1f dB', i, channel_center_freqs(i), power_db);
    if i == max_channel
        fprintf(' ← 最强');
    end
    fprintf('\n');
end

% 分析不同载频脉冲的分布
fprintf('\n载频分布分析:\n');
unique_freqs = unique(fc_10ms(1:max_pulses));
fprintf('  不同载频数量: %d\n', length(unique_freqs));
fprintf('  载频范围: %.3f - %.3f GHz\n', min(unique_freqs)/1e9, max(unique_freqs)/1e9);

% 时域分析：检测脉冲位置
fprintf('\n时域脉冲检测:\n');
envelope = abs(IF_pulse_train);
threshold = max(envelope) * 0.3;  % 30%阈值
pulse_detected = envelope > threshold;
pulse_regions = diff([0, pulse_detected, 0]);
pulse_starts = find(pulse_regions == 1);
pulse_ends = find(pulse_regions == -1) - 1;

fprintf('  检测到脉冲数量: %d\n', length(pulse_starts));
fprintf('  原始脉冲数量: %d\n', max_pulses);

%% 6. 可视化结果
fprintf('\n=== 生成图形 ===\n');

% 时间轴
t_total = (0:length(IF_pulse_train)-1) / IF_fs;
x_fre = linspace(0, IF_fs, len);

% 图1: 脉冲串时域分析
figure('Name', '脉冲串时域分析');
subplot(3,1,1);
plot(t_total*1000, real(IF_pulse_train));
title('中频脉冲串时域波形');
xlabel('时间 (ms)'); ylabel('幅度');
grid on;
xlim([0, min(10, max(t_total*1000))]);  % 显示前10ms

subplot(3,1,2);
plot(t_total*1000, abs(IF_pulse_train));
hold on;
plot([0, max(t_total*1000)], [threshold, threshold], 'r--', 'LineWidth', 2);
title('脉冲串包络');
xlabel('时间 (ms)'); ylabel('幅度');
legend('信号包络', '检测阈值');
grid on;
xlim([0, min(10, max(t_total*1000))]);

subplot(3,1,3);
% 显示载频变化
pulse_times = [];
pulse_freqs = [];
for i = 1:max_pulses
    pulse_time = pulse_info(i).start / IF_fs * 1000;  % 转换为ms
    pulse_times = [pulse_times, pulse_time];
    pulse_freqs = [pulse_freqs, pulse_info(i).fc/1e9];
end
stem(pulse_times, pulse_freqs, 'filled');
title('脉冲载频跳变');
xlabel('时间 (ms)'); ylabel('载频 (GHz)');
grid on;
xlim([0, min(10, max(t_total*1000))]);

% 图2: 频域分析
figure('Name', '脉冲串频域分析');
subplot(2,2,1);
plot(x_fre/1e6, abs(fft(IF_pulse_train)));
title('脉冲串整体频谱');
xlabel('频率 (MHz)'); ylabel('幅度');
grid on;

subplot(2,2,2);
bar(1:D, 10*log10(channel_power/max_power));
title('各信道功率分布');
xlabel('信道号'); ylabel('相对功率 (dB)');
grid on;
% 添加中心频率标签
for i = 1:D
    text(i, 10*log10(channel_power(i)/max_power) + 2, sprintf('%.1fMHz', channel_center_freqs(i)), ...
        'HorizontalAlignment', 'center', 'FontSize', 10);
end

% 信道化输出
for iter = 1:D
    subplot(2,2,2+iter);
    plot(x_fre/1e6, abs(fft(y(iter,:))));
    title(sprintf('信道 %d 输出 (中心: %.1f MHz)', iter, channel_center_freqs(iter)));
    xlabel('频率 (MHz)'); ylabel('幅度');
    grid on;
end

% 图3: 时频分析
figure('Name', '脉冲串时频分析');
% 计算短时傅里叶变换
window_length = round(0.1e-3 * IF_fs);  % 0.1ms窗口
overlap = round(window_length * 0.8);
[S, F, T] = spectrogram(IF_pulse_train, window_length, overlap, [], IF_fs);

subplot(2,1,1);
imagesc(T*1000, F/1e6, 20*log10(abs(S)));
axis xy;
colorbar;
title('脉冲串时频图');
xlabel('时间 (ms)'); ylabel('频率 (MHz)');
ylim([0, 100]);  % 关注0-100MHz范围

subplot(2,1,2);
% 各信道的时域功率变化
for i = 1:D
    channel_power_time = abs(y(i,:)).^2;
    % 平滑处理
    smooth_window = round(0.01e-3 * IF_fs);  % 0.01ms平滑窗口
    if smooth_window > 1
        channel_power_smooth = conv(channel_power_time, ones(1,smooth_window)/smooth_window, 'same');
    else
        channel_power_smooth = channel_power_time;
    end
    plot(t_total*1000, 10*log10(channel_power_smooth + eps));
    hold on;
end
title('各信道功率时间变化');
xlabel('时间 (ms)'); ylabel('功率 (dB)');
legend(arrayfun(@(x) sprintf('信道%d', x), 1:D, 'UniformOutput', false));
grid on;
xlim([0, min(10, max(t_total*1000))]);

%% 7. 性能总结
fprintf('\n=== 性能总结 ===\n');

% 计算检测率
detection_rate = length(pulse_starts) / max_pulses * 100;
fprintf('脉冲检测性能:\n');
fprintf('  检测率: %.1f%%\n', detection_rate);

% 信道利用率
fprintf('信道利用率:\n');
for i = 1:D
    utilization = channel_power(i) / sum(channel_power) * 100;
    fprintf('  信道 %d: %.1f%%\n', i, utilization);
end

% 跳频分析
fprintf('跳频特性:\n');
fprintf('  载频数量: %d\n', length(unique_freqs));
fprintf('  跳频范围: %.1f MHz\n', (max(unique_freqs)-min(unique_freqs))/1e6);

fprintf('\n=== 脉冲串处理完成 ===\n');
fprintf('🎉 完整脉冲串成功通过数字信道化接收机处理！\n');
fprintf('📊 可以观察跳频雷达的完整工作特性\n');
